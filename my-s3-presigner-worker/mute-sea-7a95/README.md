# S3 预签名 URL 生成器

这是一个 Cloudflare Worker，用于生成 AWS S3 的预签名上传 URL，支持安全的文件上传。

## API 端点

```
POST https://get-s3-url.pjharvey071.workers.dev
```

## 请求格式

### Headers
```
Content-Type: application/json
```

### Body
```json
{
  "fileType": "image/png" | "text/html"
}
```

## 响应格式

### 成功响应 (200 OK)
```json
{
  "uploadUrl": "https://dinq-share-og.s3.us-east-2.amazonaws.com/shares/uuid.png?[签名参数]",
  "publicUrl": "https://dinq-share-og.s3.amazonaws.com/shares/uuid.png"
}
```

### 错误响应 (400 Bad Request)
```
fileType is required
```

### 错误响应 (500 Internal Server Error)
```
Internal Server Error
```

## 支持的文件类型

| fileType | 文件扩展名 | 用途 |
|----------|-----------|------|
| `image/png` | `.png` | PNG 图片文件 |
| `text/html` | `.html` | HTML 文件 |

## 使用示例

### JavaScript/TypeScript
```typescript
async function getUploadUrl(fileType: string) {
  const response = await fetch('https://get-s3-url.pjharvey071.workers.dev', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ fileType }),
  });

  if (!response.ok) {
    throw new Error('Failed to get upload URL');
  }

  return await response.json();
}

// 使用示例
const { uploadUrl, publicUrl } = await getUploadUrl('image/png');
```

### cURL
```bash
# 获取 PNG 图片上传 URL
curl -X POST https://get-s3-url.pjharvey071.workers.dev \
  -H "Content-Type: application/json" \
  -d '{"fileType": "image/png"}'

# 获取 HTML 文件上传 URL
curl -X POST https://get-s3-url.pjharvey071.workers.dev \
  -H "Content-Type: application/json" \
  -d '{"fileType": "text/html"}'
```

## 文件上传流程

1. **获取预签名 URL**：调用此 API 获取 `uploadUrl` 和 `publicUrl`
2. **上传文件**：使用 `uploadUrl` 直接上传文件到 S3
3. **访问文件**：通过 `publicUrl` 公开访问上传的文件

### 完整上传示例
```typescript
// 1. 获取上传 URL
const { uploadUrl, publicUrl } = await getUploadUrl('image/png');

// 2. 上传文件到 S3
const file = new File([blob], 'image.png', { type: 'image/png' });
const uploadResponse = await fetch(uploadUrl, {
  method: 'PUT',
  body: file,
  headers: {
    'Content-Type': 'image/png',
  },
});

if (uploadResponse.ok) {
  console.log('文件上传成功！');
  console.log('访问链接：', publicUrl);
}
```

## CORS 配置

此 API 配置了 CORS，允许来自 `https://dinq.io` 的跨域请求。

## 安全说明

- 预签名 URL 有效期为 **5 分钟**（300 秒）
- 文件会上传到 `shares/` 目录下
- 文件名使用 UUID 生成，确保唯一性
- 支持的 Content-Type 受限于配置的文件类型

## 错误处理

| 状态码 | 描述 | 解决方案 |
|--------|------|----------|
| 400 | 缺少 fileType 参数 | 确保请求体包含有效的 fileType |
| 405 | 方法不允许 | 使用 POST 方法 |
| 500 | 服务器内部错误 | 检查 AWS 配置或稍后重试 |

## 开发和部署

### 本地开发
```bash
npm run dev
```

### 部署到生产环境
```bash
npm run deploy
```

### 环境变量
需要配置以下环境变量：
- `AWS_REGION`
- `AWS_ACCESS_KEY_ID`
- `AWS_SECRET_ACCESS_KEY`
- `S3_BUCKET_NAME` 