/**
 * Welcome to Cloudflare Workers! This is your first worker.
 *
 * - Run `npm run dev` in your terminal to start a development server
 * - Open a browser tab at http://localhost:8787/ to see your worker in action
 * - Run `npm run deploy` to publish your worker
 *
 * Bind resources to your worker in `wrangler.jsonc`. After adding bindings, a type definition for the
 * `Env` object can be regenerated with `npm run cf-typegen`.
 *
 * Learn more at https://developers.cloudflare.com/workers/
 */

// src/index.js

// 注意：现在我们可以直接用裸模块名导入了！
import { S3Client, PutObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

interface WorkerEnv extends Env {
  AWS_REGION: string;
  AWS_ACCESS_KEY_ID: string;
  AWS_SECRET_ACCESS_KEY: string;
  S3_BUCKET_NAME: string;
}

interface RequestBody {
  fileType: string;
  fileName?: string; // 🔥 新增可选的文件名参数
}

export default {
  async fetch(request: Request, env: WorkerEnv, ctx: ExecutionContext) {
    // 动态CORS配置：支持生产和预览分支
    const origin = request.headers.get('Origin') || '';
    const allowedOrigins = [
      'https://dinq.io',
      'https://awss3.ding-frontend-2.pages.dev'
    ];

    const isAllowedOrigin = allowedOrigins.includes(origin) ||
                           origin.endsWith('.pages.dev'); // 允许所有Cloudflare Pages预览分支

    const corsHeaders = {
      'Access-Control-Allow-Origin': '*', // 临时允许所有来源，用于调试
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    };

    if (request.method === 'OPTIONS') {
      return new Response(null, { headers: corsHeaders });
    }

    if (request.method === 'POST') {
      try {
        const s3Client = new S3Client({
          region: env.AWS_REGION,
          credentials: {
            accessKeyId: env.AWS_ACCESS_KEY_ID,
            secretAccessKey: env.AWS_SECRET_ACCESS_KEY,
          },
        });

        const { fileType, fileName } = await request.json() as RequestBody;
        if (!fileType) {
          return new Response('fileType is required', { status: 400, headers: corsHeaders });
        }

        // 🔥 使用前端指定的文件名，或fallback到随机UUID命名
        let key: string;
        if (fileName) {
          // 前端指定了文件名，直接使用
          key = `shares/${fileName}`;
        } else {
          // 保持原有的随机UUID逻辑
          const fileExtension = fileType === 'image/png' ? 'png' : 'html';
          key = `shares/${crypto.randomUUID()}.${fileExtension}`;
        }

        const command = new PutObjectCommand({
          Bucket: env.S3_BUCKET_NAME,
          Key: key,
          ContentType: fileType,
        });

        const uploadUrl = await getSignedUrl(s3Client, command, { expiresIn: 300 });
        const publicUrl = `https://${env.S3_BUCKET_NAME}.s3.${env.AWS_REGION}.amazonaws.com/${key}`;

        const responseBody = JSON.stringify({ uploadUrl, publicUrl });
        
        return new Response(responseBody, {
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders,
          },
        });

      } catch (error) {
        console.error('Error:', error);
        return new Response('Internal Server Error', { status: 500, headers: corsHeaders });
      }
    }

    return new Response('Method Not Allowed', { status: 405, headers: corsHeaders });
  },
};
